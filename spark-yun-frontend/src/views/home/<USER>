.zqy-home {
  display: flex;
  width: 100vw;
  height: 100vh;


  &.is-collapse {

    .zqy-home__sidebar {
      width: 80px;
      z-index: 999;
      .zqy-home__nav {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .zqy-home__main {
      padding-left: 80px;
    }

    .zqy-home__menu-footer {
      // width: 80px;
      padding-left: 24px;
      // height: 100px;
      // flex-direction: column;
      // justify-content: space-evenly;
    }


    .zqy-home__title {
      display: none;
    }
  }

  .zqy-home__sidebar {
    --el-transition-duration: 0.3s;

    display: flex;
    position: absolute;
    left: 0;
    top: 0;
    flex-direction: column;
    width: 220px;
    height: 100%;
    overflow: hidden;
    background-color: getCssVar('color', 'white');
    transition: width getCssVar('transition-duration') ease-in-out;
    border-right: 1px solid var(--el-border-color);
    z-index: 2000;
  }

  .zqy-home__main {
    width: 100vw;
    background-color: getCssVar('color', 'white');
    padding-left: 80px;
    box-sizing: border-box;
    transition: all getCssVar('transition-duration') ease-in-out;
  }

  .zqy-home__nav {
    width: 100%;
    height: 80px;
    box-sizing: border-box;
    padding: 10px 20px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .zqy-home__logo {
    height: 35px;
    // width: 40px;
  }

  .zqy-home__title {
    font-size: 28px;
    font-weight: bold;
    line-height: 46px;
    vertical-align: bottom;
    color: getCssVar('color', 'primary');
  }

  .zqy-home__icon {
    color: getCssVar('color', 'info');
  }

  .zqy-home__ops {
    cursor: pointer;
    &:hover {
      color: getCssVar('color', 'primary');
    }
  }

  .zyq-home__avatar {
    cursor: pointer;
    background-color: getCssVar('color', 'primary');
    color: getCssVar('color', 'white');
    font-size: getCssVar('font-size', 'extra-small');
  }

  .zqy-home__menu-wrap {
    padding: 10px 8px 60px 8px;
    box-sizing: border-box;
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    width: 100%;
    .zqy-home__menu.el-menu {
      border-right: 0;
      .el-sub-menu {
        .el-menu {
          .el-menu-item {
            padding-left: 44px;
          }
        }
      }
      .el-menu-item {
        border-radius: getCssVar('border-radius', 'base');

        &.is-active {
          background-color: getCssVar('menu', 'hover-bg-color');

          .zqy-home__text {
            font-weight: bold;
          }
        }
      }
    }
  }

  .zqy-home__menu-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    display: flex;
    // justify-content: center;
    align-items: center;
    height: 60px;
    box-sizing: border-box;
    padding: 0 16px;
    width: 100%;
    background-color: getCssVar('color', 'white');
    transition: getCssVar('transition-duration') width ease-in-out;
  }
}


.zyq-home__menu-tenant {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 12px;

  .zyq-home__menu-title {
    max-width: 72px;
  }
}

.zyq-home__menu-icon {
  cursor: pointer;

  &:hover {
    color: getCssVar('color', 'primary');
  }
}

.zyq-home__menu-avatar.el-popover.el-popper {
  min-width: 120px !important;
  width: 120px !important;
  padding: 4px;
}

.zyq-home__tenant-popover.el-popover {
  padding: 4px;
}

.zqy-home__menu-option {
  height: 32px;
  display: flex;
  align-items: center;
  padding: 0 12px;
  cursor: pointer;
  border-radius: 4px;

  .el-icon {
    margin-right: 12px;
  }

  &:hover {
    background-color: getCssVar('color-primary-light-9');
    color: getCssVar('color-primary');
  }

  .zyq-home__menu-text {
    max-width: 120px;
  }
}