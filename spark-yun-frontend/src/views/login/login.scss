.zqy-login {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  background-color: #ffffff;

  .zqy-logo-icon {
    width: 100px;
    height: auto;
    position: absolute;
    left: 44px;
    top: 36px;
    width: 170px;
  }

  .zqy-login__body {
    flex: 1;
    display: flex;
    padding: 0 20px;
    align-items: center;
  }

  .zqy-login__playground {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 6;
    height: 100%;
    margin-right: 80px;

    img {
      width: 76%;
    }
  }

  .zqy-login__main {
    display: flex;
    flex: 4;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    .zqy-login__text {
      width: 65%;
      min-width: 400px;
      text-align: center;
      margin-bottom: 12px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .zqy-login__form-wrap {
      width: 340px;
      // width: 49%;
      // padding: 50px 8%;
      padding: 46px 30px 20px;
      border-radius: 3%;
      box-shadow: 0 0 10px var(--el-border-color);
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        width: 90px;
        height: auto;
      }

      .oauth-login {
        height: 50px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        font-size: getCssVar('font-size', 'extra-small');
        width: 100%;
        .oauth-login-text {
          color: getCssVar('color', 'primary');
          cursor: pointer;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }


  .zqy-login__main__title {
    margin: 0;
    // margin: 30px 0 40px;
    line-height: 80px;
    font-weight: 600;
    color: var(--el-color-black);
    font-size: 24px;
  }

  .zqy-login__form {
    width: 100%;
    .el-form-item {
      margin-bottom: 36px;

      &.is-error {
        .el-input__wrapper {
          padding: 0 8px;
          box-shadow: none;
        }

        .zqy-login__input {
          border-color: getCssVar('color', 'danger');
          line-height: 40px;
        }
      }

      .zqy-login__input {
        height: 40px;
        .el-input__inner {
          font-size: 12px;
        }
      }

      .el-form-item__error {
        padding: 6px 0;
      }
    }
  }

  .zqy-login__input {
    border: 1px solid var(--el-border-color);
    border-radius: 3px;
    transition: all .4s linear;

    &:hover {
      border-color: getCssVar('color', 'primary');
    }

    .el-input__wrapper {
      padding: 0 8px;
      box-shadow: none;
    }
  }

  .zqy-login__btn {
    width: 100%;
    background: linear-gradient(45deg,var(--el-color-primary-light-3),var(--el-color-primary));
    // color: var(--vxe-icon-background-color);
    font-size: 13px;
    border-radius: 2px;
    height: 40px;
    &:hover {
      background-color: var(--el-color-primary);
      // color: var(--vxe-icon-background-color);
    }
  }
}

.oauth-redirect-url {
  display: flex;
  flex-wrap: wrap;
  .el-button {
    margin: 6px;
  }
}