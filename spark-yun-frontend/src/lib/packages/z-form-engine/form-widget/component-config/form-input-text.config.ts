export default {
    editConfig: {
        name: '文本输入',
        icon: 'Document',
        code: 'FormInputText'
    },
    componentConfig: {
        uuid: '16 uuid',
        type: 'simple',
        formValueCode: '',
        codeType: 'custom',
        label: '文本输入',
        placeholder: '请输入',
        disabled: false,
        required: false,
        isColumn: true,
        width: 2,
        componentType: 'FormInputText',
        valid: true
    },
    conponentSetConfig: [
        'LABEL',
        'CODE_SELECT',
        'PRIMARY_COLUMN',
        'WIDTH',
        'MAXLENGTH',
        'DEFAULTVALUE',
        'PLACEHOLDER',
        'DISABLED',
        'REQUIRED',
        'LIST_COLUMN'
    ]
}