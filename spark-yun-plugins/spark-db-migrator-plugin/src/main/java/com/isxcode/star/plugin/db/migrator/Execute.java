package com.isxcode.star.plugin.db.migrator;

import com.alibaba.fastjson.JSON;
import com.isxcode.star.api.agent.req.PluginReq;
import com.isxcode.star.api.datasource.constants.DatasourceType;
import com.isxcode.star.api.work.dto.DbMigrateConfig;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.handlers.ArrayHandler;
import org.apache.commons.dbutils.handlers.ScalarHandler;
import org.apache.logging.log4j.util.Strings;
import org.apache.spark.SparkConf;
import org.apache.spark.sql.*;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.*;

public class Execute {

    public static void main(String[] args) {

        PluginReq pluginReq = parse(args);
        DbMigrateConfig dbMigrateConfig = pluginReq.getDbMigrateConfig();
        List<String> errorTableList = new ArrayList<>();

        try (SparkSession sparkSession = initSparkSession(pluginReq.getSparkConfig())) {

            dbMigrateConfig.getSyncTables().forEach(table -> {

                // 获取建表语句
                QueryRunner qr = new QueryRunner();
                try {
                    Class.forName(dbMigrateConfig.getSourceDatabase().getDriver());
                } catch (ClassNotFoundException e) {
                    throw new RuntimeException(e);
                }

                String firstColumn;
                String createSql;
                try (Connection connection = DriverManager.getConnection(dbMigrateConfig.getSourceDatabase().getUrl(),
                    dbMigrateConfig.getSourceDatabase().getUser(), dbMigrateConfig.getSourceDatabase().getPassword())) {

                    // 获取建表语句
                    Object[] result = qr.query(connection, "SHOW CREATE TABLE " + table, new ArrayHandler());
                    createSql = String.valueOf(result[1]);

                    // 获取第一个字段
                    firstColumn =
                        qr.query(connection, "SELECT COLUMN_NAME FROM information_schema.COLUMNS WHERE TABLE_NAME = '"
                            + table + "' AND ORDINAL_POSITION = 1", new ScalarHandler<>());
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }

                try (Connection connection = DriverManager.getConnection(dbMigrateConfig.getTargetDatabase().getUrl(),
                    dbMigrateConfig.getTargetDatabase().getUser(), dbMigrateConfig.getTargetDatabase().getPassword())) {

                    // 删除表
                    qr.execute(connection, "DROP TABLE IF EXISTS " + table);

                    // 创建表
                    try {
                        qr.execute(connection, createSql);
                    } catch (SQLException e) {
                        errorTableList.add(table);
                        return;
                    }
                } catch (SQLException e) {
                    throw new RuntimeException(e);
                }

                List<String> predicates = new ArrayList<>();
                for (int i = 0; i < pluginReq.getSyncRule().getNumPartitions(); i++) {
                    predicates.add(getHashPredicate(pluginReq.getDbMigrateConfig().getSourceDBType(), firstColumn,
                        pluginReq.getSyncRule().getNumPartitions(), i, i));
                }

                // 初始化来源
                Properties sourceProp = new Properties();
                sourceProp.put("user", dbMigrateConfig.getSourceDatabase().getUser());
                if (dbMigrateConfig.getSourceDatabase().getPassword() != null) {
                    sourceProp.put("password", dbMigrateConfig.getSourceDatabase().getPassword());
                }
                sourceProp.put("driver", dbMigrateConfig.getSourceDatabase().getDriver());
                Dataset<Row> sourceData = sparkSession.read().jdbc(dbMigrateConfig.getSourceDatabase().getUrl(), table,
                    predicates.toArray(new String[0]), sourceProp);
                sourceData.createOrReplaceTempView("zhiqingyun_src_" + table);

                // 初始化去向
                DataFrameReader frameReader =
                    sparkSession.read().format("jdbc").option("driver", dbMigrateConfig.getTargetDatabase().getDriver())
                        .option("url", dbMigrateConfig.getTargetDatabase().getUrl()).option("dbtable", table)
                        .option("user", dbMigrateConfig.getTargetDatabase().getUser());
                if (dbMigrateConfig.getTargetDatabase().getPassword() != null) {
                    frameReader.option("password", dbMigrateConfig.getTargetDatabase().getPassword());
                }
                Dataset<Row> targetData = frameReader.load();
                targetData.createOrReplaceTempView("zhiqingyun_dist_" + table);

                // 执行数据同步
                sparkSession
                    .sql("insert into " + "zhiqingyun_dist_" + table + " select * from " + "zhiqingyun_src_" + table);

                // 结束打印
                System.out.println("【" + table + "】: success");
            });

            // 对失败的表进行递归处理
            while (!errorTableList.isEmpty()) {
                errorTableList.forEach(table -> {

                    // 获取建表语句
                    QueryRunner qr = new QueryRunner();
                    try {
                        Class.forName(dbMigrateConfig.getSourceDatabase().getDriver());
                    } catch (ClassNotFoundException e) {
                        throw new RuntimeException(e);
                    }

                    String firstColumn;
                    String createSql;
                    try (Connection connection = DriverManager.getConnection(dbMigrateConfig.getSourceDatabase().getUrl(),
                        dbMigrateConfig.getSourceDatabase().getUser(), dbMigrateConfig.getSourceDatabase().getPassword())) {

                        // 获取建表语句
                        Object[] result = qr.query(connection, "SHOW CREATE TABLE " + table, new ArrayHandler());
                        createSql = String.valueOf(result[1]);

                        // 获取第一个字段
                        firstColumn =
                            qr.query(connection, "SELECT COLUMN_NAME FROM information_schema.COLUMNS WHERE TABLE_NAME = '"
                                + table + "' AND ORDINAL_POSITION = 1", new ScalarHandler<>());
                    } catch (SQLException e) {
                        throw new RuntimeException(e);
                    }

                    try (Connection connection = DriverManager.getConnection(dbMigrateConfig.getTargetDatabase().getUrl(),
                        dbMigrateConfig.getTargetDatabase().getUser(), dbMigrateConfig.getTargetDatabase().getPassword())) {

                        // 删除表
                        qr.execute(connection, "DROP TABLE IF EXISTS " + table);

                        // 创建表
                        try {
                            qr.execute(connection, createSql);
                        } catch (SQLException e) {
                            return;
                        }
                    } catch (SQLException e) {
                        throw new RuntimeException(e);
                    }

                    List<String> predicates = new ArrayList<>();
                    for (int i = 0; i < pluginReq.getSyncRule().getNumPartitions(); i++) {
                        predicates.add(getHashPredicate(pluginReq.getDbMigrateConfig().getSourceDBType(), firstColumn,
                            pluginReq.getSyncRule().getNumPartitions(), i, i));
                    }

                    // 初始化来源
                    Properties sourceProp = new Properties();
                    sourceProp.put("user", dbMigrateConfig.getSourceDatabase().getUser());
                    if (dbMigrateConfig.getSourceDatabase().getPassword() != null) {
                        sourceProp.put("password", dbMigrateConfig.getSourceDatabase().getPassword());
                    }
                    sourceProp.put("driver", dbMigrateConfig.getSourceDatabase().getDriver());
                    Dataset<Row> sourceData = sparkSession.read().jdbc(dbMigrateConfig.getSourceDatabase().getUrl(), table,
                        predicates.toArray(new String[0]), sourceProp);
                    sourceData.createOrReplaceTempView("zhiqingyun_src_" + table);

                    // 初始化去向
                    DataFrameReader frameReader =
                        sparkSession.read().format("jdbc").option("driver", dbMigrateConfig.getTargetDatabase().getDriver())
                            .option("url", dbMigrateConfig.getTargetDatabase().getUrl()).option("dbtable", table)
                            .option("user", dbMigrateConfig.getTargetDatabase().getUser());
                    if (dbMigrateConfig.getTargetDatabase().getPassword() != null) {
                        frameReader.option("password", dbMigrateConfig.getTargetDatabase().getPassword());
                    }
                    Dataset<Row> targetData = frameReader.load();
                    targetData.createOrReplaceTempView("zhiqingyun_dist_" + table);

                    // 执行数据同步
                    sparkSession
                        .sql("insert into " + "zhiqingyun_dist_" + table + " select * from " + "zhiqingyun_src_" + table);

                    // 结束打印
                    System.out.println("【" + table + "】: success");
                    errorTableList.remove(table);
                });
            }
        }
    }

    public static PluginReq parse(String[] args) {
        if (args.length == 0) {
            throw new RuntimeException("args is empty");
        }
        return JSON.parseObject(Base64.getDecoder().decode(args[0]), PluginReq.class);
    }

    public static SparkConf initSparkConf(Map<String, String> sparkConfig) {
        SparkConf conf = new SparkConf();
        if (sparkConfig != null) {
            for (Map.Entry<String, String> entry : sparkConfig.entrySet()) {
                conf.set(entry.getKey(), entry.getValue());
            }
        }
        return conf;
    }

    public static SparkSession initSparkSession(Map<String, String> sparkConfig) {

        SparkSession.Builder sparkSessionBuilder = SparkSession.builder();

        SparkConf conf = initSparkConf(sparkConfig);

        if (Strings.isEmpty(sparkConfig.get("hive.metastore.uris"))) {
            return sparkSessionBuilder.config(conf).getOrCreate();
        } else {
            return sparkSessionBuilder.config(conf).enableHiveSupport().getOrCreate();
        }
    }

    public static String getHashPredicate(String datasourceType, String PartitionColumn, Integer NumPartitions,
                                          Integer startIndex, Integer endIndex) {

        switch (datasourceType) {
            case DatasourceType.MYSQL:
            case DatasourceType.TIDB:
                return "CRC32(`" + PartitionColumn + "`) % " + NumPartitions + " in (" + startIndex + ",-" + endIndex
                    + ")";
            case DatasourceType.ORACLE:
                return "MOD(ORA_HASH(\"" + PartitionColumn + "\")," + NumPartitions + ") in (" + startIndex + ",-"
                    + endIndex + ")";
            case DatasourceType.OCEANBASE:
            case DatasourceType.DM:
                return "ORA_HASH(`" + PartitionColumn + "`) % " + NumPartitions + " in (" + startIndex + ",-" + endIndex
                    + ")";
            case DatasourceType.SQL_SERVER:
                return "CHECKSUM(" + PartitionColumn + ") % " + NumPartitions + " in (" + startIndex + ",-" + endIndex
                    + ")";
            case DatasourceType.POSTGRE_SQL:
            case DatasourceType.OPEN_GAUSS:
            case DatasourceType.GAUSS:
                return "hashtext(cast(\"" + PartitionColumn + "\" as TEXT)) % " + NumPartitions + " in (" + startIndex
                    + ",-" + endIndex + ")";
            case DatasourceType.CLICKHOUSE:
                return "sipHash64(`" + PartitionColumn + "`) % " + NumPartitions + " in (" + startIndex + ",-"
                    + endIndex + ")";
            case DatasourceType.HIVE:
                return "hash(`" + PartitionColumn + "`) % " + NumPartitions + " in (" + startIndex + ",-" + endIndex
                    + ")";
            case DatasourceType.HANA_SAP:
                return "MOD(HASH_SHA256(`" + PartitionColumn + "`), " + NumPartitions + ") in (" + startIndex + ",-"
                    + endIndex + ")";
            case DatasourceType.DORIS:
            case DatasourceType.STAR_ROCKS:
                return "murmur_hash3_32(`" + PartitionColumn + "`) % " + NumPartitions + " in (" + startIndex + ",-"
                    + endIndex + ")";
            case DatasourceType.DB2:
                return "MOD(hash8(`" + PartitionColumn + "`)," + NumPartitions + ") in (" + startIndex + ",-" + endIndex
                    + ")";
            case DatasourceType.H2:
                return "MOD(ORA_HASH(`" + PartitionColumn + "`), " + NumPartitions + ") in (" + startIndex + ",-"
                    + endIndex + ")";
            default:
                throw new RuntimeException("暂不支持的数据库");
        }
    }
}
